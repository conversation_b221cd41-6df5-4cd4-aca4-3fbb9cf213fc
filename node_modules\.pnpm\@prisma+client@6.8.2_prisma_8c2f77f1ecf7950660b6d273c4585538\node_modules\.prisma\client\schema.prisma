// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// ============================================================================
// AUTHENTICATION & USER MANAGEMENT
// ============================================================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  image     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Better Auth fields
  emailVerified <PERSON>olean @default(false)

  // Relations
  accounts      Account[]
  sessions      Session[]
  organizations OrganizationMember[]
  invitations   OrganizationInvitation[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// ============================================================================
// MULTI-TENANT ORGANIZATION SYSTEM
// ============================================================================

model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Subscription & Billing
  stripeCustomerId       String?
  stripeSubscriptionId   String?
  stripePriceId          String?
  stripeCurrentPeriodEnd DateTime?

  // Relations
  members     OrganizationMember[]
  invitations OrganizationInvitation[]

  @@map("organizations")
}

enum OrganizationRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}

model OrganizationMember {
  id             String           @id @default(cuid())
  organizationId String
  userId         String
  role           OrganizationRole @default(MEMBER)
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([organizationId, userId])
  @@map("organization_members")
}

model OrganizationInvitation {
  id             String           @id @default(cuid())
  organizationId String
  email          String
  role           OrganizationRole @default(MEMBER)
  token          String           @unique
  expires        DateTime
  createdAt      DateTime         @default(now())
  invitedById    String

  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  invitedBy    User         @relation(fields: [invitedById], references: [id], onDelete: Cascade)

  @@unique([organizationId, email])
  @@map("organization_invitations")
}

// ============================================================================
// AUDIT LOGS & SECURITY
// ============================================================================

model AuditLog {
  id             String   @id @default(cuid())
  organizationId String?
  userId         String?
  action         String
  resource       String
  resourceId     String?
  metadata       Json?
  ipAddress      String?
  userAgent      String?
  createdAt      DateTime @default(now())

  @@index([organizationId])
  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@map("audit_logs")
}
