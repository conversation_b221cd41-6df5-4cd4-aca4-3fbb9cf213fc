import { Shield } from "lucide-react";
import Link from "next/link";
import { AuthErrorBoundary } from "@/components/auth/auth-error-boundary";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen grid lg:grid-cols-2">
      {/* Left side - Branding */}
      <div className="hidden lg:flex lg:flex-col lg:justify-center lg:px-12 bg-muted">
        <div className="mx-auto w-full max-w-sm">
          <Link href="/" className="flex items-center space-x-2 mb-8">
            <Shield className="h-8 w-8 text-primary" />
            <span className="text-2xl font-bold">TikPay</span>
          </Link>
          <h1 className="text-3xl font-bold tracking-tight">
            Secure payment processing for the modern web
          </h1>
          <p className="mt-4 text-lg text-muted-foreground">
            Join thousands of businesses already using TikPay to accept payments globally with confidence.
          </p>
          <div className="mt-8 space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full" />
              <span className="text-sm text-muted-foreground">Bank-level security</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full" />
              <span className="text-sm text-muted-foreground">99.99% uptime guarantee</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full" />
              <span className="text-sm text-muted-foreground">24/7 support</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Auth forms */}
      <div className="flex flex-col justify-center px-4 py-12 sm:px-6 lg:px-8">
        <div className="mx-auto w-full max-w-sm">
          <div className="lg:hidden mb-8">
            <Link href="/" className="flex items-center space-x-2">
              <Shield className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">TikPay</span>
            </Link>
          </div>
          <AuthErrorBoundary>
            {children}
          </AuthErrorBoundary>
        </div>
      </div>
    </div>
  );
}
