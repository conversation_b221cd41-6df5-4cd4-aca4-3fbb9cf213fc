# Database Configuration
# Get your Supabase database URL from: https://supabase.com/dashboard/project/_/settings/database
DATABASE_URL="postgresql://postgres.eaahxsgnwmfwmvdisbmc:<EMAIL>:6543/postgres?pgbouncer=true"

# Direct connection to the database. Used for migrations
DIRECT_URL="postgresql://postgres.eaahxsgnwmfwmvdisbmc:<EMAIL>:5432/postgres"
# Authentication Configuration
# Generate a random 32+ character string for production
BETTER_AUTH_SECRET="your-super-secret-auth-key-min-32-chars"
BETTER_AUTH_URL="http://localhost:3000"

# Email Configuration (Resend)
# Get your API key from: https://resend.com/api-keys
RESEND_API_KEY="re_your_resend_api_key"
RESEND_FROM_EMAIL="<EMAIL>"

# Stripe Configuration
# Get your keys from: https://dashboard.stripe.com/apikeys
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# Supabase Configuration
# Get these from: https://supabase.com/dashboard/project/_/settings/api
NEXT_PUBLIC_SUPABASE_URL="https://eaahxsgnwmfwmvdisbmc.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVhYWh4c2dud21md212ZGlzYm1jIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMDQ5NjAsImV4cCI6MjA2MzU4MDk2MH0.2wzKpkQFuEScGNBRI0_YGKc69qkR6nANAR476PTmsF0"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVhYWh4c2dud21md212ZGlzYm1jIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODAwNDk2MCwiZXhwIjoyMDYzNTgwOTYwfQ.NfY0YZuygfe_1nFbavaFzkEo-7FhSKf_5qFkcOXuakA"

# Stripe Public Configuration
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="Now.TS Inspired Boilerplate"

# Development
NODE_ENV="development"

# Optional: Skip environment validation during build (useful for Docker)
# SKIP_ENV_VALIDATION=true
