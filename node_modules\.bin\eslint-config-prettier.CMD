@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\boilerplate\node_modules\.pnpm\eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_\node_modules\eslint-config-prettier\bin\node_modules;C:\Users\<USER>\boilerplate\node_modules\.pnpm\eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\boilerplate\node_modules\.pnpm\eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_\node_modules;C:\Users\<USER>\boilerplate\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\boilerplate\node_modules\.pnpm\eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_\node_modules\eslint-config-prettier\bin\node_modules;C:\Users\<USER>\boilerplate\node_modules\.pnpm\eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\boilerplate\node_modules\.pnpm\eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_\node_modules;C:\Users\<USER>\boilerplate\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_\node_modules\eslint-config-prettier\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\eslint-config-prettier@10.1.5_eslint@9.27.0_jiti@2.4.2_\node_modules\eslint-config-prettier\bin\cli.js" %*
)
